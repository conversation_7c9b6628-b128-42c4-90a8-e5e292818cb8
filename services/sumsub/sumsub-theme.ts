import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";

/**
 * Creates a comprehensive Sumsub theme configuration that matches TrustNexus design system
 * This theme ensures visual consistency between the mobile app and Sumsub SDK flows
 */
export const createSumsubTheme = () => {
  return {
    universal: {
      fonts: {
        assets: [
          {
            name: "SpaceMono",
            file: "www/fonts/SpaceMono-Regular.ttf",
          },
        ],
        // Apply SpaceMono font to all text elements for brand consistency
        headline1: {
          name: "SpaceMono",
          size: scale(24),
        },
        headline2: {
          name: "SpaceMono",
          size: scale(20),
        },
        subtitle1: {
          name: "SpaceMono",
          size: scale(18),
        },
        subtitle2: {
          name: "SpaceMono",
          size: scale(16),
        },
        body: {
          name: "SpaceMono",
          size: scale(14),
        },
        caption: {
          name: "SpaceMono",
          size: scale(12),
        },
      },
      colors: {
        // Background colors matching TrustNexus design
        backgroundCommon: COLORS.background, // #FFFFFF
        backgroundNeutral: COLORS.grayLight, // #E3E3E3

        // Primary button colors - using TrustNexus primary green
        primaryButtonBackground: COLORS.primary, // #1C3F3C
        primaryButtonContent: COLORS.background, // #FFFFFF
        primaryButtonBackgroundPressed: COLORS.primaryLight, // #C8E2CE
        primaryButtonBackgroundDisabled: COLORS.mediumGray, // #888E92
        primaryButtonContentDisabled: COLORS.background,

        // Secondary button colors - outlined style with primary color
        secondaryButtonBackground: COLORS.background, // #FFFFFF
        secondaryButtonContent: COLORS.primary, // #1C3F3C
        secondaryButtonBorder: COLORS.primary, // #1C3F3C
        secondaryButtonBackgroundPressed: COLORS.primaryLight, // #C8E2CE
        secondaryButtonContentPressed: COLORS.primary,

        // Content text colors following TrustNexus hierarchy
        contentStrong: COLORS.secondary, // #151312
        contentNeutral: COLORS.gray, // #60636B
        contentWeak: COLORS.mediumGray, // #888E92

        // Status colors with appropriate opacity for backgrounds
        backgroundSuccess: COLORS.success + "1F", // #2AA952 with 12% opacity
        backgroundWarning: COLORS.warning + "21", // #FF9500 with 13% opacity
        backgroundCritical: COLORS.error + "1F", // #FF0000 with 12% opacity

        // Navigation elements using primary color
        navigationBarItem: COLORS.primary, // #1C3F3C
        alertTint: COLORS.primary, // #1C3F3C
        toolbarTint: COLORS.primary,
        toolbarBackground: COLORS.background,

        // Camera interface - dark theme for better UX
        cameraBackground: COLORS.secondary, // #151312
        cameraContent: COLORS.background, // #FFFFFF
        cameraBackgroundOverlay: COLORS.secondary + "BF", // #151312 with 75% opacity

        // Form fields styling
        fieldBackground: COLORS.grayLight, // #E3E3E3
        fieldBorder: COLORS.grayLight, // #E3E3E3
        fieldContent: COLORS.secondary, // #151312
        fieldPlaceholder: COLORS.mediumGray, // #888E92

        // Card styling options
        cardBorderedBackground: COLORS.background, // #FFFFFF
        cardBorderedBorder: COLORS.grayLight, // #E3E3E3
        cardFilledBackground: COLORS.grayLight, // #E3E3E3

        // List styling
        listSeparator: COLORS.grayLight, // #E3E3E3
        listItemBackground: COLORS.background, // #FFFFFF
        listItemBackgroundPressed: COLORS.grayLight, // #E3E3E3

        // Bottom sheet styling
        bottomSheetBackground: COLORS.background, // #FFFFFF
        bottomSheetHandle: COLORS.grayLight, // #E3E3E3

        // Additional critical colors that might cause blue fallbacks
        fieldTint: COLORS.primary, // #1C3F3C - Controls field focus/selection colors
        progressBarTint: COLORS.primary, // #1C3F3C - Progress indicators
        progressBarBackground: COLORS.primary + "33", // #1C3F3C with 20% opacity

        // Link button colors
        linkButtonContent: COLORS.primary, // #1C3F3C
        linkButtonContentPressed: COLORS.primaryLight, // #C8E2CE

        // Additional field states
        fieldBorderDisabled: COLORS.mediumGray, // #888E92
        fieldBorderFocused: COLORS.primary, // #1C3F3C
        fieldBackgroundInvalid: COLORS.error + "1F", // #FF0000 with 12% opacity

        // List item selection
        listSelectedItemBackground: COLORS.primaryLight, // #C8E2CE

        // Card states
        cardPlainBackground: COLORS.background, // #FFFFFF
      },
      metrics: {
        // Button dimensions matching TrustNexus design (AppButton style)
        buttonHeight: verticalScale(50), // Match AppButton total height (56 + 16*2 padding)
        buttonCornerRadius: scale(28), // Match AppButton rounded-full style
        buttonBorderWidth: scale(1),

        // Card styling
        cardCornerRadius: scale(8),
        cardBorderWidth: scale(1),

        // Field styling matching app forms
        fieldHeight: verticalScale(48), // Smaller height for input fields
        fieldCornerRadius: scale(8), // Keep fields less rounded than buttons
        fieldBorderWidth: scale(1),

        // Layout spacing consistent with app
        screenHorizontalMargin: scale(16),

        // Bottom sheet styling
        bottomSheetCornerRadius: scale(16),
        bottomSheetHandleSize: {
          width: scale(36),
          height: scale(4),
        },

        // Document capture frame
        documentFrameCornerRadius: scale(14),
        documentFrameBorderWidth: scale(2),

        // Selfie viewport
        viewportBorderWidth: scale(8),

        // Card styles for different components
        verificationStepCardStyle: "filled",
        documentTypeCardStyle: "filled",
        selectedCountryCardStyle: "filled",
        supportItemCardStyle: "bordered",

        // Text alignment
        screenHeaderAlignment: "center",
        sectionHeaderAlignment: "left",
      },
    },
    ios: {
      colors: {
        // iOS-specific color overrides to prevent blue fallbacks
        tintColor: COLORS.primary, // #1C3F3C - iOS tint color
        barTintColor: COLORS.background, // #FFFFFF - Navigation bar background
        navigationBarTintColor: COLORS.primary, // #1C3F3C - Navigation bar items
      },
      metrics: {
        commonStatusBarStyle: "default",
        cameraStatusBarStyle: "default",
        preferredCloseBarItemAlignment: "right",
        preferredCloseBarItemStyle: "icon",
        respectsPreferredContentSizeCategory: true,
      },
    },
    android: {
      colors: {
        // Android-specific color overrides to prevent blue fallbacks
        accentColor: COLORS.primary, // #1C3F3C - Android accent color
        colorPrimary: COLORS.primary, // #1C3F3C - Android primary color
        colorPrimaryDark: COLORS.secondary, // #151312 - Android primary dark
        colorAccent: COLORS.primary, // #1C3F3C - Android accent
      },
      metrics: {
        activityIndicatorStyle: "medium",
        listSeparatorHeight: scale(1),
        listSeparatorMarginLeft: scale(0),
        listSeparatorMarginRight: scale(0),
      },
    },
  };
};

/**
 * Creates a minimal theme configuration for testing purposes
 * Use this for initial testing before applying the full theme
 */
export const createMinimalSumsubTheme = () => {
  return {
    universal: {
      colors: {
        primaryButtonBackground: COLORS.primary,
        primaryButtonContent: COLORS.background,
        backgroundCommon: COLORS.background,
        contentStrong: COLORS.secondary,
      },
      metrics: {
        buttonHeight: verticalScale(88), // Match AppButton total height
        buttonCornerRadius: scale(28), // Match AppButton rounded-full
      },
    },
  };
};

/**
 * Creates an enhanced theme with additional customization options
 * Includes more detailed styling for advanced use cases
 */
export const createEnhancedSumsubTheme = () => {
  const baseTheme = createSumsubTheme();

  return {
    ...baseTheme,
    universal: {
      ...baseTheme.universal,
      images: {
        // Custom icons can be added here if needed
        // iconClose: "www/images/close-icon.png",
        // iconCamera: "www/images/camera-icon.png",
      },
      colors: {
        ...baseTheme.universal.colors,
        // Additional color customizations
        errorContent: COLORS.error,
        successContent: COLORS.success,
        warningContent: COLORS.warning,

        // Enhanced button states
        primaryButtonBackgroundHover: COLORS.primaryLight,
        secondaryButtonBackgroundHover: COLORS.grayLight,

        // Enhanced field states
        fieldBackgroundFocused: COLORS.background,
        fieldBorderFocused: COLORS.primary,
        fieldBackgroundError: COLORS.error + "0F", // 6% opacity
        fieldBorderError: COLORS.error,

        // Enhanced card states
        cardBackgroundSelected: COLORS.primaryLight,
        cardBorderSelected: COLORS.primary,
      },
      metrics: {
        ...baseTheme.universal.metrics,
        // Enhanced spacing
        sectionSpacing: verticalScale(24),
        elementSpacing: verticalScale(16),

        // Enhanced animations (if supported)
        animationDuration: 200,

        // Enhanced accessibility
        minimumTouchTarget: scale(44),
      },
    },
  };
};
