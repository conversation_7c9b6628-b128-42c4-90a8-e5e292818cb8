"use client";

import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { SUMSUB_LEVEL } from "@/types/user";
import SumsubWebSdk from "@sumsub/websdk-react";
import { useEffect, useState } from "react";

interface SumSubIframeProps {
  token: string | undefined;
  email: string;
  sumsubLevel: SUMSUB_LEVEL;
  onApplicantReviewComplete: () => void;
  className?: string;
  isLivenessCheck?: boolean;
  onLivenessFailure?: () => void;
}

const SumSubIframe = ({
  token,
  email,
  sumsubLevel,
  onApplicantReviewComplete,
  className,
  isLivenessCheck = false,
  onLivenessFailure,
}: SumSubIframeProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const messageHandler = async (message: string, payload: any) => {
    console.log("message =>", message);
    console.log("payload =>", payload);

    if (isLivenessCheck) {
      // Handle liveness check completion with proper success/failure validation
      if (
        payload?.reviewStatus === "completed" &&
        payload?.levelName === "live"
      ) {
        console.log("🔍 Liveness check completed, checking result...");

        // Check if the liveness check was actually approved
        if (
          payload?.reviewResult?.reviewAnswer === "GREEN" ||
          payload?.reviewResult?.rejectLabels?.length === 0
        ) {
          console.log("✅ Liveness check approved, proceeding to consent...");
          setTimeout(() => {
            onApplicantReviewComplete();
          }, 3000);
        } else {
          console.log("❌ Liveness check failed or rejected");
          console.log("Review result:", payload?.reviewResult);

          // Call failure handler if provided
          if (onLivenessFailure) {
            setTimeout(() => {
              onLivenessFailure();
            }, 1000);
          }
        }
      }
      return;
    }

    if (payload?.reviewStatus === "completed") {
      setTimeout(() => {
        onApplicantReviewComplete();
      }, 3000);
    }
  };

  const errorHandler = (e: any) => {
    console.log("Error", e);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className={`w-full max-h-[80dvh] overflow-y-auto ${className}`}>
      {email && token && (
        <SumsubWebSdk
          className="min-w-full max-w-xl h-full !ml-0"
          accessToken={token}
          expirationHandler={() => getSumSubToken(sumsubLevel)}
          config={{
            lang: "en", //language of WebSDK texts and comments (ISO 639-1 format)
            email: email,
          }}
          style={{
            marginLeft: "0 !important",
          }}
          options={{ addViewportTag: false, adaptIframeHeight: true }}
          onMessage={messageHandler}
          onError={errorHandler}
        />
      )}
    </div>
  );
};

export default SumSubIframe;
