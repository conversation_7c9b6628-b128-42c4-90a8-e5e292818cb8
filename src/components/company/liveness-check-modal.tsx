"use client";

import React, { useEffect } from "react";
import { X } from "lucide-react";
import { FormCard } from "@/components/register/form-card";
import SumSubIframe from "@/components/sumsub-iframe";
import { SUMSUB_LEVEL } from "@/types/user";
import { useGetUserProfile } from "@/react-query/auth-hooks";

interface LivenessCheckModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  companyName: string;
  token: string | null;
  isLoadingToken: boolean;
}

export const LivenessCheckModal: React.FC<LivenessCheckModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  token,
  isLoadingToken,
}) => {
  const { data: userProfile } = useGetUserProfile();

  useEffect(() => {
    if (isOpen) {
      // Disable body scrolling when modal is open
      document.body.style.overflow = "hidden";
    } else {
      // Re-enable body scrolling when modal is closed
      document.body.style.overflow = "";
    }

    // Cleanup body style on unmount
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const handleLivenessComplete = () => {
    onClose();
    onSuccess();
  };

  const handleLivenessFailure = () => {
    console.log(
      "🚫 Liveness check failed - closing modal without proceeding to consent"
    );
    onClose();
    // Don't call onSuccess() - this prevents progression to consent modal
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      {/* Modal Container */}
      <div className="relative w-[90%] max-w-[32rem] bg-white rounded-[1.25rem] border border-[#C8E2CE] shadow-[1.375rem_2.5rem_2.5rem_rgba(0,0,0,0.05)] p-6 flex flex-col items-center max-h-[90vh] overflow-hidden">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute left-6 top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 shadow-md focus:ring-2 focus:ring-primary focus:outline-none"
        >
          <X className="w-5 h-5 text-black" />
        </button>

        {/* Title */}
        <div className="mt-4 text-xl font-poppins font-medium text-black">
          Liveness Verification
        </div>

        {/* Scrollable Content Section */}
        <div className="flex-1 w-full overflow-y-hidden scrollbar-none">
          {isLoadingToken ? (
            <p className="text-primary text-base text-center">Loading...</p>
          ) : !token ? (
            <p className="text-red-500 text-base text-center">No token found</p>
          ) : (
            <FormCard className="p-5 w-full border-none shadow-none">
              {token && userProfile?.email && (
                <SumSubIframe
                  token={token}
                  email={userProfile.email}
                  sumsubLevel={SUMSUB_LEVEL.LIVE}
                  onApplicantReviewComplete={handleLivenessComplete}
                  onLivenessFailure={handleLivenessFailure}
                  className="h-auto max-h-[110dvh] overflow-y-auto"
                  isLivenessCheck={true}
                />
              )}
            </FormCard>
          )}
        </div>
      </div>
    </div>
  );
};
