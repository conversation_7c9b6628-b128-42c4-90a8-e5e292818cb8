import React, { useState, useEffect } from "react";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import type { Company } from "@/types/company";
import { CompanyHeader } from "@/components/company/company-header";
import { ButtonsSection } from "@/components/company/modal-buttons";
import { UpgradeKycModal } from "@/components/company/upgrade-kyc-modal";
import { ShareKycModal } from "@/components/company/share-kyc-modal";
import UpgradeKycProcessModal from "@/components/company/upgrade-kyc-process-modal";
import { LivenessCheckModal } from "@/components/company/liveness-check-modal";
import { useFetchDocumentHistory } from "@/react-query/consent-history-hook";
import { useRevokeConsent } from "@/react-query/revoke-consent-hook";
import { RevokeConsentModal } from "@/components/revoke-consent-modal";
import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { getClientSumSubToken } from "@/utils/client-sumsub-token";
import { SUMSUB_LEVEL } from "@/types/user";

interface CompanyDetailsProps {
  companyData: Company | null;
}

export const CompanyDetails: React.FC<CompanyDetailsProps> = ({
  companyData,
}) => {
  const data = useGetUserProfile();
  const { data: histories = [] } = useFetchDocumentHistory();

  const [isRevokeModalOpen, setRevokeModalOpen] = useState(false);
  const [isProcessing, setProcessing] = useState(false);
  const [livenessToken, setLivenessToken] = useState<string | null>(null);
  const [isLoadingToken, setIsLoadingToken] = useState(false);

  const { mutateAsync: revokeConsent } = useRevokeConsent({
    mutationOptions: {
      onError: (err) => {
        console.error("Failed to revoke consent:", err.message);
      },
    },
  });

  const confirmRevoke = async (companyId: number) => {
    setProcessing(true);
    try {
      await revokeConsent(companyId);
      setRevokeModalOpen(false);
    } catch (err) {
      console.error("Revoke consent failed:", err);
    } finally {
      setProcessing(false);
    }
  };

  const isCompanyInHistory = histories.some(
    (history) => history.company_id === companyData?.company_id,
  );

  const [isUpgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [isShareModalOpen, setShareModalOpen] = useState(false);
  const [isKycProcessModalOpen, setKycProcessModalOpen] = useState(false);
  const [isLivenessCheckModalOpen, setLivenessCheckModalOpen] = useState(false);
  const [livenessCheckAction, setLivenessCheckAction] = useState<'upgrade' | 'share' | null>(null);

  const getTierNumber = (state?: string) => {
    if (!state) return 0;
    return parseInt(state.replace(/[^\d]/g, "")) || 0;
  };

  // Fetch liveness token when needed
  const fetchLivenessToken = async () => {
    if (livenessToken) return livenessToken; // Return existing token if available
    
    setIsLoadingToken(true);
    try {
      // Check if user is company type and has beneficiary information
      const isCompanyUser = data?.data?.registrationtype === "company";
      const beneficiaryUserId = data?.data?.userCompany?.beneficiaryUserId;
      const beneficiaryApplicantId = data?.data?.userCompany?.beneficiaryApplicantId;
      
      console.log("Liveness - User type:", data?.data?.registrationtype);
      console.log("Liveness - Is company user:", isCompanyUser);
      console.log("Liveness - Beneficiary User ID:", beneficiaryUserId);
      
      let token;
      
      if (isCompanyUser && beneficiaryUserId && beneficiaryApplicantId) {
        // For company users, use the beneficiary userId with client-side function
        console.log("Liveness - Using beneficiary userId for company user:", beneficiaryUserId);
        token = await getClientSumSubToken(SUMSUB_LEVEL.LIVE, beneficiaryUserId, beneficiaryApplicantId);
      } else {
        // For individual users or companies without beneficiary info, use regular token
        console.log("Liveness - Using regular token for individual user or company without beneficiary");
        token = await getSumSubToken(SUMSUB_LEVEL.LIVE);
      }
      
      setLivenessToken(token);
      return token;
    } catch (error) {
      console.error("Failed to fetch liveness token:", error);
      return null;
    } finally {
      setIsLoadingToken(false);
    }
  };

  if (!companyData) {
    return <p>Company not found</p>;
  }

  const isIndividual = data?.data?.registrationtype === "individual";
  const requiredTier = isIndividual ? companyData.required_individual_tier : companyData.required_corporate_tier;
  const isGreyedOut = +requiredTier > getTierNumber(data?.data?.state);

  const handleApplyKyc = async () => {
    setUpgradeModalOpen(false);
    // Directly open the KYC process modal for upgrade
    setKycProcessModalOpen(true);
  };

  const handleShareKyc = async () => {
    // Only proceed with liveness check if isGreyedOut is false AND user tier is same or lower than required
    if (!isGreyedOut) {
     
      const token = await fetchLivenessToken();
      if (token) {
        setLivenessCheckAction('share');
        setLivenessCheckModalOpen(true);
      } else {
        // User tier is higher than required, skip liveness check
        setShareModalOpen(true);
      }
    }
  };

  const handleLivenessSuccess = async () => {
    setLivenessCheckModalOpen(false);
    
    if (livenessCheckAction === 'upgrade') {
      setKycProcessModalOpen(true);
    } else if (livenessCheckAction === 'share') {
      setShareModalOpen(true);
    }
    
    setLivenessCheckAction(null);
  };

  const handleLivenessClose = () => {
    setLivenessCheckModalOpen(false);
    setLivenessCheckAction(null);
  };

  return (
    <div className="w-full overflow-hidden flex flex-col gap-10 md:gap-32">
      {/* Content Wrapper */}
      <div
        className={`relative p-0 pt-6 md:pt-12 ${
          isGreyedOut ? "opacity-50 pointer-events-none" : ""
        }`}
      >
        {/* Header */}
        <CompanyHeader
        // @ts-ignore
          logo={companyData.s3ImageUrl || companyData.s3LogoUrl}
          name={companyData.name}
          description={companyData.description}
          tier={requiredTier}
        />
      </div>

      {/* Buttons */}
      <ButtonsSection
        isGreyedOut={isGreyedOut}
        isCompanyInHistory={isCompanyInHistory}
        onUpgradeClick={() => setUpgradeModalOpen(true)}
        onShareClick={handleShareKyc}
        handleRevoke={() => setRevokeModalOpen(true)}
      />

      {/* Modals */}
      <UpgradeKycModal
        isOpen={isUpgradeModalOpen}
        companyName={companyData.name}
        companyLogo={companyData.s3LogoUrl || companyData.logo}
        tierNumber={requiredTier}
        onClose={() => setUpgradeModalOpen(false)}
        onApplyKyc={handleApplyKyc}
      />
      
      <LivenessCheckModal
        isOpen={isLivenessCheckModalOpen}
        companyName={companyData.name}
        onClose={handleLivenessClose}
        onSuccess={handleLivenessSuccess}
        token={livenessToken}
        isLoadingToken={isLoadingToken}
      />
      
      <ShareKycModal
        isOpen={isShareModalOpen}
        companyData={companyData}
        onClose={() => setShareModalOpen(false)}
      />
      
      <UpgradeKycProcessModal
        isOpen={isKycProcessModalOpen}
        onClose={() => setKycProcessModalOpen(false)}
      />

      {/* Revoke Consent Modal */}

      <RevokeConsentModal
          isOpen={isRevokeModalOpen}
          onClose={() => setRevokeModalOpen(false)}
          onConfirm={() => confirmRevoke(companyData?.company_id)}
          companyName={companyData.name}
          isLoading={isProcessing}
        />
    </div>
  );
};
