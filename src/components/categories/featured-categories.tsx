"use client";

import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useRouter, usePathname } from "next/navigation";
import { categories } from "@/data/categories";
import { cn } from "@/utils/tailwind";

interface FeaturedCategoriesProps {
  onCategorySelect?: (slug: string | null) => void;
  containerClassName?: string;
  selectedCategorySlug?: string | null;
}

export function FeaturedCategories({
  onCategorySelect,
  containerClassName,
  selectedCategorySlug,
}: FeaturedCategoriesProps) {
  const router = useRouter();
  const pathname = usePathname();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(selectedCategorySlug || null);
  const [visibleItemCount, setVisibleItemCount] = useState(2);

  // Update local state when prop changes
  useEffect(() => {
    setSelectedCategory(selectedCategorySlug || null);
  }, [selectedCategorySlug]);

  const calculateVisibleItems = useCallback(() => {
    if (!scrollContainerRef.current) return;
    const container = scrollContainerRef.current;
    const containerWidth = container.offsetWidth;
    const isMobile = containerWidth < 1024;
    
    // Fixed number of items per slide: 2 for mobile/tablet, 3 for desktop
    const count = isMobile ? 2 : 3;
    setVisibleItemCount(count);
  }, []);

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;
    const container = scrollContainerRef.current;
    
    // Calculate page width based on fixed card count
    const containerWidth = container.offsetWidth;
    const pageWidth = containerWidth;
    const scrollPosition = container.scrollLeft;

    // Set active index based on scroll position
    const rawIndex = scrollPosition / pageWidth;
    const newIndex = Math.round(rawIndex);
    const maxIndex = Math.ceil(categories.length / visibleItemCount) - 1;
    setActiveIndex(Math.max(0, Math.min(newIndex, maxIndex)));
  }, [visibleItemCount]);

  const scrollTo = (index: number) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const containerWidth = container.offsetWidth;
      const scrollLeft = index * containerWidth;

      container.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      });
      
      setActiveIndex(index);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    calculateVisibleItems();

    if (container) {
      container.addEventListener("scroll", handleScroll);
      window.addEventListener("resize", calculateVisibleItems);

      return () => {
        container.removeEventListener("scroll", handleScroll);
        window.removeEventListener("resize", calculateVisibleItems);
      };
    }
  }, [handleScroll, calculateVisibleItems]);

  const handleCategoryClick = (index: number) => {
    const category = categories[index];
    if (category) {
      if (pathname === '/categories') {
        if (selectedCategory === category.slug) {
          setSelectedCategory(null);
          onCategorySelect && onCategorySelect(null);
        } else {
          setSelectedCategory(category.slug);
          onCategorySelect && onCategorySelect(category.slug);
        }
      } else {
        router.push(`/categories?category=${category.slug}`);
      }
    }
  };

  // Calculate page count
  const pageCount = Math.max(1, Math.ceil(categories.length / visibleItemCount));

  return (
    <section className="relative overflow-visible md:py-6">
      <div className={cn("mx-auto px-4 lg:px-16 relative max-w-7xl", containerClassName)}>
        {/* Navigation Buttons - Hidden on mobile/tablet */}
        <div className="hidden lg:block">
          <button
            onClick={() => scrollTo(Math.max(0, activeIndex - 1))}
            className="absolute -left-0 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center transition-colors disabled:opacity-50 bg-white rounded-full shadow-md hover:shadow-lg border border-gray-200"
            disabled={activeIndex === 0}
          >
            <ChevronLeft className="h-6 w-6 text-primary" />
          </button>
          <button
            onClick={() => scrollTo(Math.min(pageCount - 1, activeIndex + 1))}
            className="absolute -right-0 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center disabled:opacity-50 bg-white rounded-full shadow-md hover:shadow-lg border border-gray-200"
            disabled={activeIndex === pageCount - 1}
          >
            <ChevronRight className="h-6 w-6 text-[#1c3f3c]" />
          </button>
        </div>

        {/* Scrollable Categories */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide no-scrollbar pb-4"
          style={{ scrollSnapType: 'x mandatory' }}
        >
          <div className="flex">
            {Array.from({ length: pageCount }).map((_, pageIndex) => (
              <div
                key={pageIndex}
                className="flex-shrink-0 w-full grid gap-3 lg:gap-4"
                style={{
                  gridTemplateColumns: `repeat(${visibleItemCount}, 1fr)`,
                  scrollSnapAlign: 'start',
                }}
              >
                {categories
                  .slice(pageIndex * visibleItemCount, (pageIndex + 1) * visibleItemCount)
                  .map((category, index) => (
                    <button
                      key={category.id}
                      onClick={() => handleCategoryClick(pageIndex * visibleItemCount + index)}
                      className={`h-[80px] md:h-[94px] bg-white text-black ${
                        selectedCategory === category.slug
                          ? "border-2 border-black"
                          : "border-[3px] border-[#C8E2CE]"
                      } rounded-2xl flex items-center justify-between gap-4 px-4 md:px-6 lg:px-8 transition-all duration-300 ease-in-out hover:shadow-md shadow-sm`}
                    >
                      <div className="flex flex-col items-start text-left">
                        <p className="text-sm md:text-base font-semibold leading-tight">
                          {category.title}
                        </p>
                        <p className="text-xs md:text-sm font-normal leading-tight mt-1 text-gray-600">
                          {category.subtitle}
                        </p>
                      </div>
                      <Image
                        src={category.icon ?? "/placeholder.svg"}
                        alt={category.title}
                        width={32}
                        height={32}
                        className="w-8 h-8 md:w-10 md:h-10 flex-shrink-0"
                      />
                    </button>
                  ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pagination Dots */}
      {pageCount > 1 && (
        <div className="flex justify-center mt-4 space-x-1.5">
          {Array.from({ length: pageCount }).map((_, index) => (
            <button
              key={index}
              onClick={() => scrollTo(index)}
              className={`w-1.5 h-1.5 rounded-full transition-colors duration-300 ${
                activeIndex === index ? "bg-primary" : "bg-secondary"
              }`}
            />
          ))}
        </div>
      )}
    </section>
  );
}
