"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { Header } from "@/components/categories/navbar";
import { CategoryBreadcrumbs } from "@/components/categories/breadcrumbs";
import { FeaturedCategories } from "@/components/categories/featured-categories";
import { Background } from "@/components/categories/background";
import { CompanyList } from "@/components/categories/company-list";
import { AlphabetNavigation } from "@/components/categories/alphabet-navigation";
import { Footer } from "@/components/layout/footer";

function CategoriesPageContent() {
  const searchParams = useSearchParams();
  const [selectedCategorySlug, setSelectedCategorySlug] = useState<
    string | null
  >(null);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Initialize selected category from URL parameters
  useEffect(() => {
    const categoryFromUrl = searchParams.get('category');
    if (categoryFromUrl) {
      setSelectedCategorySlug(categoryFromUrl);
    }
  }, [searchParams]);

  const handleCategorySelect = (slug: string | null) => {
    setSelectedCategorySlug(slug || null);
  };

  const handleLetterSelect = (letter: string | null) => {
    setSelectedLetter(letter === selectedLetter ? null : letter);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <div className="container min-h-screen flex flex-col md:px-8">
      <Background />
      <Header 
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
      />
      <main className="flex-1 overflow-hidden flex flex-col">
        <div className="w-full lg:pr-20 flex flex-col flex-1">
          <CategoryBreadcrumbs />
          <FeaturedCategories 
            onCategorySelect={handleCategorySelect} 
            selectedCategorySlug={selectedCategorySlug}
          />
          
          {/* Tablet Alphabet Navigation */}
          <div className="hidden md:block lg:hidden">
            <AlphabetNavigation
              selectedLetter={selectedLetter}
              setSelectedLetter={handleLetterSelect}
            />
          </div>
          
          {/* Mobile Alphabet Navigation */}
          <div className="md:hidden mt-4 -mx-4 overflow-x-auto scrollbar-hide">
            <AlphabetNavigation
              selectedLetter={selectedLetter}
              setSelectedLetter={handleLetterSelect}
            />
          </div>
          
          <div className="flex-1 min-h-0 border-y border-y-black py-6">
            <CompanyList
              selectedCategorySlug={selectedCategorySlug}
              filterByLetter={selectedLetter}
              searchQuery={searchQuery}
            />
          </div>
        </div>
      </main>
      
      {/* Desktop Alphabet Navigation - Fixed position */}
      <div className="hidden lg:block">
        <AlphabetNavigation
          selectedLetter={selectedLetter}
          setSelectedLetter={handleLetterSelect}
        />
      </div>
      <Footer />
    </div>
  );
}

export default function LandingPage() {
  return (
    <Suspense fallback={
      <div className="container min-h-screen flex flex-col md:px-8">
        <div className="flex items-center justify-center py-8">
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      </div>
    }>
      <CategoriesPageContent />
    </Suspense>
  );
}
