import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";
import { RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import Modal from "react-native-modal";
import { runLivenessCheckProcess } from "@/services/sumsub/launchSNSMobileSDK";
import { getLivenessCheckToken } from "@/services/sumsub/sumsub-service";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useCreateUserCompanyConsent } from "@/services/company/company-hooks";
import { useRefetchUserData } from "@/hooks/useRefetchUserData";

interface OnboardingConfirmModalProps {
  companyName: string;
  companyLogo?: string;
  companyTierNumber: number;
  companyId: number;
}

export const OnboardingConfirmModal = ({
  companyName,
  companyId,
}: OnboardingConfirmModalProps) => {
  const { refreshUserData } = useRefetchUserData();
  const [isVisible, setIsVisible] = useState(false);
  const { data: user } = useCurrentUser();

  const isKyb = user?.registrationtype === RegistrationTypeOption.KYB;

  // Consent creation mutation
  const { mutate: createUserCompanyConsent, isPending } =
    useCreateUserCompanyConsent({
      onSuccess: () => {
        console.log("✅ Consent created successfully after liveness check");
        setIsVisible(false);
        refreshUserData();
      },
      onError: () => {
        console.error("❌ Failed to create consent");
      },
    });
  const onClose = () => {
    setIsVisible(false);
  };

  const closeModal = () => {
    setIsVisible(false);
  };

  const triggerLiveCheck = async () => {
    if (!user?.email || !user?.user_id || !user?.applicant_id) {
      console.error("❌ Missing user information for onboarding");
      return;
    }

    try {
      console.log("🎯 Starting onboarding liveness check for:", companyName);
      console.log(
        "👤 User type:",
        isKyb ? "KYB (Company)" : "KYC (Individual)"
      );

      // Step 1: Liveness check with automatic user type detection
      // This works for both KYC and KYB users, matching web implementation
      await runLivenessCheckProcess({
        userEmail: user.email,
        changeHandler: async (event) => {
          console.log(
            `📱 Liveness Status: ${event.prevStatus} → ${event.newStatus}`
          );

          if (event.newStatus === "Approved") {
            console.log("✅ Liveness check approved, creating consent...");
            setIsVisible(true);
          } else if (event.newStatus === "Failed") {
            console.error("❌ Liveness check failed");
          }
        },
      });
    } catch (error) {
      console.error("❌ Error during onboarding liveness check:", error);
      console.log(
        "🔄 Liveness check failed, allowing user to proceed without verification"
      );

      // Fallback: Allow user to proceed without liveness check
      // This applies to both KYC and KYB users
      setIsVisible(true);
    }
  };

  const onConfirm = async () => {
    createUserCompanyConsent({
      userId: user?.user_id || "",
      companyId: companyId, // Already number type from props
      applicant_id: user?.applicant_id || "",
    });
  };

  return (
    <View>
      <AppButton text="ONBOARD" size="lg" onPress={triggerLiveCheck} />
      <Modal
        isVisible={isVisible}
        onBackdropPress={closeModal}
        onSwipeComplete={closeModal}
        onBackButtonPress={closeModal}
      >
        <View style={styles.modalContent}>
          <AppText size="xl" weight="bold" style={styles.title}>
            Are you sure you want to apply to onboard with {companyName}?
          </AppText>

          <AppText style={styles.description}>
            By confirming, you are giving consent to share your personal
            identity information with
            <AppText weight="bold"> {companyName}</AppText>. You can revoke this
            consent at any time in your user profile page.
          </AppText>

          <View style={styles.actions}>
            <AppButton
              text="Confirm"
              variant="default"
              size="sm"
              onPress={onConfirm}
              isLoading={isPending}
              disabled={isPending}
            />
            <AppButton
              text="Cancel"
              variant="ghost"
              size="sm"
              onPress={onClose}
              disabled={isPending}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(24),
    width: "100%",
    maxWidth: scale(400),
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  description: {
    textAlign: "center",
    marginBottom: verticalScale(24),
    color: COLORS.gray,
  },
  actions: {
    gap: verticalScale(12),
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
