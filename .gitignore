# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.idea
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
.open-next
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.dev.vars

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Don't ignore migrations
!migrations/
